<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jdl.mcp</groupId>
        <artifactId>mcp-platform</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>mcp-server</artifactId>
    <packaging>jar</packaging>
    <name>MCP :: Platform :: Server</name>
    <dependencies>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jsf</artifactId>
        </dependency>
        <!-- JSF Open API -->
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jsf-api</artifactId>
            <version>2.2.85</version>
        </dependency>
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.jdl.mcp</groupId>
            <artifactId>mcp-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdl.mcp</groupId>
            <artifactId>mcp-solon</artifactId>
        </dependency>
        <!-- Spring -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <!-- Redis -->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>4.3.1</version>
        </dependency>

        <!-- MySQL -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.28</version>
        </dependency>

        <!-- Apache Commons DBCP2 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-dbcp2</artifactId>
            <version>2.9.0</version>
        </dependency>

        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>
        <!-- JSON 处理依赖 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.15.3</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.15.3</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.15.3</version>
        </dependency>

        <!-- org.json 依赖 (用于 JSONObject) -->
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20230227</version>
        </dependency>

        <!-- 使用纯Java实现OCR，不需要外部依赖 -->

    </dependencies>
    <build>
        <finalName>mcp-server</finalName>
    </build>
</project>
