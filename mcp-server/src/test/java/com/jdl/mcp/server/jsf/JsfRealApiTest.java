package com.jdl.mcp.server.jsf;

import com.jd.jsf.open.api.vo.InterfaceInfo;
import com.jdl.mcp.server.jsf.auth.JsfAuthService;
import com.jdl.mcp.server.jsf.model.JsfResult;
import com.jdl.mcp.server.jsf.param.BaseJsfRequest;
import com.jdl.mcp.server.jsf.service.JsfInterfaceService;
import com.jdl.mcp.server.jsf.service.JsfProviderAliasService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JSF真实API功能测试
 * 使用真实的JSF开放API进行功能验证
 * 
 * <AUTHOR> Assistant
 */
@SpringBootTest(classes = {com.jdl.mcp.server.McpPorterApplication.class})
@ActiveProfiles("test")
@ImportResource("classpath:spring-jsf-test.xml")
class JsfRealApiTest {
    
    // 测试用的真实接口
    private static final String TEST_INTERFACE = "erp.ql.station.api.service.gangao.CustomsClearanceApi";
    private static final String TEST_OPERATOR = "testOperator";
    
    @Autowired(required = false)
    private JsfAuthService jsfAuthService;
    
    @Autowired(required = false)
    private JsfInterfaceService jsfInterfaceService;
    
    @Autowired(required = false)
    private JsfProviderAliasService jsfProviderAliasService;
    
    @Test
    void testJsfAuthServiceWithRealConfig() {
        assertNotNull(jsfAuthService, "JsfAuthService should be configured");
        
        // 测试构建通用参数
        BaseJsfRequest commonParams = jsfAuthService.buildCommonParams(TEST_OPERATOR);
        
        assertNotNull(commonParams);
        assertEquals("jdos_mcp-platform", commonParams.getAppKey());
        assertEquals(TEST_OPERATOR, commonParams.getOperator());
        assertNotNull(commonParams.getClientIp());
        assertNotNull(commonParams.getTimeStamp());
        assertNotNull(commonParams.getSign());
        assertEquals(32, commonParams.getSign().length()); // MD5长度
        
        System.out.println("=== 认证参数测试结果 ===");
        System.out.println("AppKey: " + commonParams.getAppKey());
        System.out.println("Operator: " + commonParams.getOperator());
        System.out.println("ClientIP: " + commonParams.getClientIp());
        System.out.println("TimeStamp: " + commonParams.getTimeStamp());
        System.out.println("Sign: " + commonParams.getSign());
    }
    
    @Test
    void testGetInterfaceInfoWithRealApi() {
        if (jsfInterfaceService == null) {
            System.out.println("JsfInterfaceService not configured, skipping test");
            return;
        }
        
        System.out.println("=== 查询接口信息测试 ===");
        System.out.println("查询接口: " + TEST_INTERFACE);
        
        try {
            JsfResult<InterfaceInfo> result = jsfInterfaceService.getByInterfaceName(TEST_INTERFACE, TEST_OPERATOR);
            
            assertNotNull(result);
            assertNotNull(result.getCode());
            assertNotNull(result.getMsg());
            
            System.out.println("调用结果:");
            System.out.println("Code: " + result.getCode());
            System.out.println("Message: " + result.getMsg());
            System.out.println("Success: " + result.isSuccess());
            
            if (result.isSuccess()) {
                InterfaceInfo interfaceInfo = result.getData();
                if (interfaceInfo != null) {
                    System.out.println("接口信息:");
                    System.out.println("  ID: " + interfaceInfo.getId());
                    System.out.println("  接口名: " + interfaceInfo.getInterfaceName());
                    System.out.println("  服务类型: " + interfaceInfo.getServiceType());
                    System.out.println("  消费者总数: " + interfaceInfo.getConsumerTotal());
                    System.out.println("  提供者总数: " + interfaceInfo.getProviderTotal());
                    System.out.println("  存活提供者: " + interfaceInfo.getProviderLive());
                    System.out.println("  存活消费者: " + interfaceInfo.getConsumerLive());
                    System.out.println("  部门: " + interfaceInfo.getDepartment());
                    System.out.println("  负责人: " + interfaceInfo.getOwnerUser());
                    System.out.println("  重要性: " + interfaceInfo.getImportant());
                    System.out.println("  状态: " + interfaceInfo.getValid());
                } else {
                    System.out.println("接口信息为空");
                }
            } else {
                System.out.println("调用失败: " + result.getMsg());
            }
            
        } catch (Exception e) {
            System.out.println("调用异常: " + e.getMessage());
            e.printStackTrace();
            // 在测试环境中，这可能是预期的行为
        }
    }
    
    @Test
    void testGetProviderAliasWithRealApi() {
        if (jsfProviderAliasService == null) {
            System.out.println("JsfProviderAliasService not configured, skipping test");
            return;
        }
        
        System.out.println("=== 查询接口别名测试 ===");
        System.out.println("查询接口: " + TEST_INTERFACE);
        
        try {
            JsfResult<List<String>> result = jsfProviderAliasService.getAliasByInterfaceName(TEST_INTERFACE, TEST_OPERATOR);
            
            assertNotNull(result);
            assertNotNull(result.getCode());
            assertNotNull(result.getMsg());
            
            System.out.println("调用结果:");
            System.out.println("Code: " + result.getCode());
            System.out.println("Message: " + result.getMsg());
            System.out.println("Success: " + result.isSuccess());
            
            if (result.isSuccess()) {
                List<String> aliases = result.getData();
                if (aliases != null && !aliases.isEmpty()) {
                    System.out.println("找到 " + aliases.size() + " 个别名:");
                    for (int i = 0; i < aliases.size(); i++) {
                        System.out.println("  " + (i + 1) + ". " + aliases.get(i));
                    }
                } else {
                    System.out.println("没有找到别名");
                }
                
                if (result.getTotal() != null) {
                    System.out.println("总数: " + result.getTotal());
                }
            } else {
                System.out.println("调用失败: " + result.getMsg());
            }
            
        } catch (Exception e) {
            System.out.println("调用异常: " + e.getMessage());
            e.printStackTrace();
            // 在测试环境中，这可能是预期的行为
        }
    }
    
    @Test
    void testGetMethodInfoWithRealApi() {
        if (jsfInterfaceService == null) {
            System.out.println("JsfInterfaceService not configured, skipping test");
            return;
        }
        
        System.out.println("=== 查询方法信息测试 ===");
        System.out.println("查询接口: " + TEST_INTERFACE);
        
        try {
            JsfResult<String> result = jsfInterfaceService.getMethodInfo(
                TEST_INTERFACE, null, null, null, null, TEST_OPERATOR);
            
            assertNotNull(result);
            assertNotNull(result.getCode());
            assertNotNull(result.getMsg());
            
            System.out.println("调用结果:");
            System.out.println("Code: " + result.getCode());
            System.out.println("Message: " + result.getMsg());
            System.out.println("Success: " + result.isSuccess());
            
            if (result.isSuccess()) {
                String methodInfo = result.getData();
                if (methodInfo != null && !methodInfo.isEmpty()) {
                    System.out.println("方法信息:");
                    System.out.println(methodInfo);
                } else {
                    System.out.println("方法信息为空");
                }
            } else {
                System.out.println("调用失败: " + result.getMsg());
            }
            
        } catch (Exception e) {
            System.out.println("调用异常: " + e.getMessage());
            e.printStackTrace();
            // 在测试环境中，这可能是预期的行为
        }
    }
    
    @Test
    void testGetMethodListWithRealApi() {
        if (jsfInterfaceService == null) {
            System.out.println("JsfInterfaceService not configured, skipping test");
            return;
        }
        
        System.out.println("=== 查询方法列表测试 ===");
        System.out.println("查询接口: " + TEST_INTERFACE);
        
        try {
            JsfResult<List<String>> result = jsfInterfaceService.getMethodList(
                TEST_INTERFACE, null, null, null, null, TEST_OPERATOR);
            
            assertNotNull(result);
            assertNotNull(result.getCode());
            assertNotNull(result.getMsg());
            
            System.out.println("调用结果:");
            System.out.println("Code: " + result.getCode());
            System.out.println("Message: " + result.getMsg());
            System.out.println("Success: " + result.isSuccess());
            
            if (result.isSuccess()) {
                List<String> methods = result.getData();
                if (methods != null && !methods.isEmpty()) {
                    System.out.println("找到 " + methods.size() + " 个方法:");
                    for (int i = 0; i < methods.size(); i++) {
                        System.out.println("  " + (i + 1) + ". " + methods.get(i));
                    }
                } else {
                    System.out.println("没有找到方法");
                }
            } else {
                System.out.println("调用失败: " + result.getMsg());
            }
            
        } catch (Exception e) {
            System.out.println("调用异常: " + e.getMessage());
            e.printStackTrace();
            // 在测试环境中，这可能是预期的行为
        }
    }
    
    @Test
    void testSignatureGeneration() {
        assertNotNull(jsfAuthService, "JsfAuthService should be configured");
        
        System.out.println("=== 签名生成测试 ===");
        
        // 测试签名的一致性
        String appKey = "jdos_mcp-platform";
        Long timeStamp = 1234567890L;
        String token = "Jk7J2Lp9XmN4QwR8vT3sB6yH1gF5dE0z";
        
        String sign1 = jsfAuthService.generateSign(appKey, timeStamp, token);
        String sign2 = jsfAuthService.generateSign(appKey, timeStamp, token);
        
        assertEquals(sign1, sign2, "相同参数应该生成相同的签名");
        assertEquals(32, sign1.length(), "MD5签名长度应该是32位");
        
        System.out.println("AppKey: " + appKey);
        System.out.println("TimeStamp: " + timeStamp);
        System.out.println("Token: " + token);
        System.out.println("Generated Sign: " + sign1);
        
        // 测试不同时间戳生成不同签名
        String sign3 = jsfAuthService.generateSign(appKey, timeStamp + 1, token);
        assertNotEquals(sign1, sign3, "不同时间戳应该生成不同的签名");
        
        System.out.println("Different TimeStamp Sign: " + sign3);
    }
}
