package com.jdl.mcp.server;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

@SpringBootApplication
@ComponentScan(basePackages = {"com.example", "com.jdl.mcp.server.openme", "com.jdl.mcp.server.jsf"})
public class McpPorterApplication {

    public static void main(String[] args) {
        SpringApplication.run(McpPorterApplication.class, args);
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
