package com.jdl.mcp.server.jsf.auth.impl;

import com.jdl.mcp.server.jsf.auth.JsfAuthService;
import com.jdl.mcp.server.jsf.exception.JsfErrorCode;
import com.jdl.mcp.server.jsf.exception.JsfException;
import com.jdl.mcp.server.jsf.param.BaseJsfRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

/**
 * JSF认证服务实现类
 * 
 * <AUTHOR> Assistant
 */
@Service
public class JsfAuthServiceImpl implements JsfAuthService {
    
    private static final Logger log = LoggerFactory.getLogger(JsfAuthServiceImpl.class);
    
    @Value("${jsf.openapi.appKey:testAppName}")
    private String appKey;
    
    @Value("${jsf.openapi.token:testAppToken}")
    private String token;
    
    @Override
    public BaseJsfRequest buildCommonParams(String operator) {
        try {
            long timeStamp = System.currentTimeMillis();
            String clientIp = getLocalHost();
            String sign = generateSign(appKey, timeStamp, token);
            
            BaseJsfRequest request = new BaseJsfRequest();
            request.setAppKey(appKey);
            request.setOperator(operator);
            request.setClientIp(clientIp);
            request.setTimeStamp(timeStamp);
            request.setSign(sign);
            
            log.debug("Built common params: appKey={}, operator={}, clientIp={}, timeStamp={}", 
                     appKey, operator, clientIp, timeStamp);
            
            return request;
        } catch (Exception e) {
            log.error("Failed to build common params", e);
            throw new JsfException(JsfErrorCode.CONFIG_ERROR, "Failed to build common params", e);
        }
    }
    
    @Override
    public String generateSign(String appKey, Long timeStamp, String token) {
        if (appKey == null || timeStamp == null || token == null) {
            throw new JsfException(JsfErrorCode.PARAM_ERROR, "appKey, timeStamp and token cannot be null");
        }
        
        try {
            String content = appKey + timeStamp + token;
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(content.getBytes(StandardCharsets.UTF_8));
            
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            
            String sign = sb.toString();
            log.debug("Generated sign for content: {}, sign: {}", content, sign);
            return sign;
        } catch (Exception e) {
            log.error("Failed to generate sign", e);
            throw new JsfException(JsfErrorCode.SIGN_ERROR, "Failed to generate sign", e);
        }
    }
    
    @Override
    public String getLocalHost() {
        try {
            return InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            log.warn("Failed to get local host, using default", e);
            return "127.0.0.1";
        }
    }
}
