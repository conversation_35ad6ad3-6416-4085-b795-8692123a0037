package com.jdl.mcp.server.servers.autoprogramming;

import com.jdl.mcp.core.service.annotation.McpEndpoint;
import com.jdl.mcp.core.service.annotation.Tool;
import com.jdl.mcp.core.service.annotation.ToolParam;
import com.jdl.mcp.core.service.enmus.Role;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.core.io.Resource;

import java.util.Properties;

/**
 * 提供自动化编程提示词服务
 * <p>
 * 此服务提供不同类型的自动化编程提示词
 * </p>
 * <p>
 * 此服务由 JoyCoder 更新
 * 更新时间: 2025-05-09 17:55:00
 * </p>
 */
@Component
@McpEndpoint(name = "autoprogramming", description = "提供自动化编程提示词服务")
public class AutoProgrammingMcpTool {

    @Autowired
    private ApplicationContext context;

    @Tool(name = "get_prompt", description = "获取指定类型的自动化编程提示词", audience = {Role.ASSISTANT})
    public String getPrompt(@ToolParam(name = "type", description = "提示词类型，如 'vue', 'react', 'java' 等") String type) {
        Properties properties = loadPrompts();
        String prompt = properties.getProperty(type.toLowerCase());
        return prompt != null ? prompt : "未知的提示词类型。支持的类型包括：" + String.join(", ", properties.stringPropertyNames());
    }

    private Properties loadPrompts() {
        YamlPropertiesFactoryBean yaml = new YamlPropertiesFactoryBean();
        Resource resource = new ClassPathResource("prompts.yml");
        yaml.setResources(resource);
        return yaml.getObject();
    }
}
