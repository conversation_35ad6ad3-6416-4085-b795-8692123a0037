spring:
  profiles:
    active: '@profile.active@'
    include: swagger
  application:
    name: mcp-platform
      # 引入外部配置文件
  # config:
  #     import:
  #       - classpath:chat-service.yml
  # 静态资源配置
  web:
    resources:
      static-locations:
        - classpath:/static/
        - classpath:/public/
      cache:
        period: 3600
  # MVC配置
  mvc:
    static-path-pattern: /**
  # Thymeleaf配置
  thymeleaf:
    cache: false
    mode: HTML
    encoding: UTF-8
    servlet:
      content-type: text/html
  solon:
    app:
      config: solon-server.yml
server:
  port: 8080
  servlet:
    context-path: /
    jsp:
      registered: false  # 禁用JSP
# 公共jsf部分
jsf:
  port: 21600
  index: ${JSF_INDEX:test.i.jsf.jd.local}
  openapi:
    appKey: ${JSF_APP_KEY:jdos_mcp-platform}
    token: ${JSF_TOKEN:Jk7J2Lp9XmN4QwR8vT3sB6yH1gF5dE0z}
    index: ${JSF_OPENAPI_INDEX:test.i.jsf.jd.local}
# SSO鉴权部分
sso:
  clientId:
  clientSecret:
  excludePath: /static,/favicon,/denied,/mcp
#susf
usf:
  tenementCode: omg
  appCode: omg
  userSystemType: 1

# 日志配置
logging:
  config: classpath:logback-spring.xml
  level:
    root: INFO
    com.jdl.mcp: DEBUG
    com.jdl.mcp.admin.ai: DEBUG
    com.jdl.mcp.server.servers.jsforder: DEBUG
    org.springframework.beans.factory: DEBUG

# joyspace 和 timeline 相关配置
openme:
  app:
    key: JIAOYI-FDM
    secret: FE253BA8F75A634F
    openTeamId: 15441406156a21eddea382ea3c2c2fbe
    teamId: '00046419'
    host: http://openme.jd.local
  joyspace:
    scene: JDLTradeAi
    userId: org.wljy1
    teamId: b10vXr-LbPQ60n1pb7D3
    x_stage: PRE
  timline:
    appId: JIAOYI-FDM
    tenantId: CN.JD.GROUP
    robotId: 00_55e779bf635b4f14
  joywork:
    app: ee
    bizCode: wxlH2M7IMWlJzJZw9BKBaf3aLow6vq
    openUserId: org.wljy1
    teamId: '00046419'